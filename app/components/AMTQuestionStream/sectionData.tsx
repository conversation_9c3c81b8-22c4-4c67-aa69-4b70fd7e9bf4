import * as Question from '@/app/components/AMTQuestionStream/Question';
import { PracGenPhysics1CombinedDeduplicated } from './Question.physics.deduplicated';
import { PracInductionCombinedDeduplicated } from './Question.PracInductionCombined.deduplicated';
import {PracHardwareFinalCombinedDeduplicated} from './Question.HardwareFinal.deduplicated';
import {PracAirsystemPracticeCombinedDeduplicated} from './Question.AirsystemPracticeCombined.deduplicated';
import {PracHardwareFinalCombinedExperimental} from './Question.HardwareFinalCombined.experiment'
import { PracExahustPracticeCombinedDeduplicated } from './Question.ExahustPracticeCombined.deduplicated';
import { PracPowerplantElectricalPracticeCombinedDeduplicated } from './Question.PowerplantElectricalPracticeCombined.deduplicated';
import { PracStarterSystemsPracticeCombinedDeduplicated } from './Question.StarterSystemsPracticeCombined.deduplicated';
import {PracAAAircraftElectricalSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAAircraftElectricalSystems';
import {PracAAAircraftFuelSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAAircraftFuelSystemsCombined';
import {PracAAAircraftLandingGearSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAAircraftLandingGearSystemsCombined';
import {PracAAHydraulicandPneumaticPowerSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAHydraulicandPneumaticPowerSystemsCombined';
import {PracAACabinAtmosphereControlSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AACabinAtmosphereControlSystemsCombined';
import {PracAAAircraftInstrumentSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAAircraftInstrumentSystemsCombined';
import {PracAACommunicationandNavigationSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AACommunicationandNavigationSystemsCombined';
import {PracAAPositionandWarningSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAPositionandWarningSystems';
import {PracAAIceandRainControlSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAIceandRainControlSystems';
import {PracAAFireProtectionSystems} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAFireProtectionSystems';
import {PracAAWoodStructures} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAWoodStructuresCombined';
import {PracAAAircraftCovering} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAAircraftCoveringCombined';
import {PracAAAircraftFinishes} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAAircraftFinishesCombined';
import {PracAASheetMetalandNonMetallicStructures} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AASheetMetalandNonMetallicStructuresCombined';
import {PracAAWelding} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAWeldingCombined';
import {PracAAAssemblyandRigging} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAAssemblyandRiggingCombined';
import {PracAAAirframeInspection} from '@/app/components/AMTQuestionStream/ASAQuestionBanks/Question.AAAirframeInspectionCombined';
import {PracInstrumentCombinedDeduplicated} from '@/app/components/AMTQuestionStream/Question.InstrumentCombined.deduplicated'

import { PracPropellerPracticeCombinedDeduplicated } from '@/app/components/AMTQuestionStream/Question.PropellerPracticeCombined.deduplicated';

import { FireProtectionCombinedDeduplicated } from './Question.FireProtectionCombined.deduplicated';
import { PracPowerPlantInspectionsCombinedDeduplicated } from './Question.PowerPlantInspectionsCombined.deduplicated';
// This data could also come from an API call
export const mechanicTestsData = [
    // Helper function to shuffle an array
    () => {
        const shuffleArray = (array: any[]) => {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
            return array;
        };  
        return shuffleArray;
    },
    {
        type: 'header',
        text: 'General',
    },
    {
        type: 'testItem',
        icon: 'NutIcon',
        title: 'Fundamentals of Electricity and Electronics',
        active: true,
        //questions: [{"elec prac 1": Question.ElectricPrac1 }, {"experimental": Question.ElectricPrac1Experimental}]
        questions: Question.ElectricPrac1Experimental
        // questions: Question.ElectricPrac1 
    },
    {
        type: 'testItem',
        icon: 'RulerIcon',
        title: 'Aircraft Drawings',
        active: true,
        questions: Question.PracGenFinalDrawing1Experimental
        // questions: [{"Practical Questions": Question.PracGenFinalDrawings1}, {"experimental": Question.PracGenFinalDrawing1Experimental}]
    },
    {
        type: 'testItem',
        icon: 'ScalesIcon',
        title: 'Weight and Balance',
        active: true,
        questions: Question.BalancePrac1Experimental
        //questions: [{"balancePrac1": Question.BalancePrac1},{"Experimental": Question.BalancePrac1Experimental}]
        //questions: Question.BalancePrac1
    },
    {
        type: 'testItem',
        icon: 'CylinderIcon',
        title: 'Fluid Lines and Fittings',
        active: true,
        questions: Question.FluidAndFitFinalPrac1
    },
    {
        type: 'testItem',
        icon: 'BriefcaseMetalIcon',
        title: 'Aircraft Materials, Hardware and Processes',
        active: true,
        questions: [{ "Practical Experimental": PracHardwareFinalCombinedExperimental}]
        // questions: [{"Practical Questions": Question.HardwarePractical}, {"Required Reading": Question.HardwareReading}, {"Assesment 1": Question.HardwareAss1}, {"Final Assesment - 2 wrong": Question.HardwareFinalAss}, {"Assesment 2": Question.HardwareAss2}, {"Assesment 3": Question.HardwareAss3}, {"Assesment 4": Question.HardwareAss4}, {"Assesment 5": Question.HardwareAss5}, {"Assesment 6": Question.HardwareAss6}, {"Practical Questions Final Combined": PracHardwareFinalCombinedDeduplicated }, { "Practical Experimental": PracHardwareFinalCombinedExperimental}]

    },
    {
        type: 'testItem',
        icon: 'TruckIcon',
        title: 'Ground Operations and Servicing',
        active: true,
        questions: Question.operations
    },
    {
        type: 'testItem',
        icon: 'BroomIcon',
        title: 'Cleaning and Corrosion Control',
        active: true,
        questions: Question.corrosion
    },
    {
        type: 'testItem',
        icon: 'MagnifyingGlassIcon', // First MagnifyingGlassIcon
        title: 'Mathematics',
        active: true,
        questions: [{"Practical Questions": Question.PracGenFinalMath1}]
    },
    {
        type: 'testItem',
        icon: 'CalculatorIcon',
        title: 'Regulations, Maintenance Forms, Records and Publications',
        active: true,
        questions: [{"Practical Questions": Question.PracGenFinalRegs1}]
    },
    {
        type: 'testItem',
        icon: 'ClipboardIcon',
        title: 'Physics for Aviation',
        active: true,
        questions: PracGenPhysics1CombinedDeduplicated
    },
    {
        type: 'testItem',
        icon: 'MagnetIcon',
        title: 'Inspection Concepts and Techniques',
        active: true,
        questions: [{"Practical Questions": Question.InspectionPractical}, {"Required Reading": Question.InspectionReading}, {"Assesment 1": Question.InspectionAss1}, {"Final Assesment - 2 wrong": Question.InspectionFinalAss}, {"Assesment 2": Question.InspectionAss2}, {"Practice Test 1": Question.InspectionPrac1}, {"Practice Test 2": Question.InspectionPrac2}, {"Practice Test 3": Question.InspectionPrac3}, {"Practice Test 4": Question.InspectionPrac4}]
    },
    {
        type: 'testItem',
        icon: 'MagnifyingGlassIcon', // Second MagnifyingGlassIcon
        title: 'Human Factors',
        active: true,
        questions: [{"Practical Questions": Question.humanFactorsPractical}, {"Required Reading": Question.humanFactorsReading}, {"Assesment 1": Question.humanFactorsAss1}, {"Final Assesment": Question.humanFactorsFinalAss}, {"Assesment 2": Question.humanFactorsAss2}, {"Final Prac Test 2": Question.PracGenFinalHumanFactors2}]
    },
    {
        type: 'header',
        text: 'Airframe',
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Wood Structures',
        active: true,
        questions: PracAAWoodStructures
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Aircraft Covering',
        active: true,
        questions: PracAAAircraftCovering
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Aircraft Finishes',
        active: true,
        questions: PracAAAircraftFinishes
    },
    {
        type: 'testItem',
        icon: 'BriefcaseMetalIcon',
        title: 'Sheet Metal & Non-Metallic Structures',
        active: true,
        questions: PracAASheetMetalandNonMetallicStructures
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Welding',
        active: true,
        questions: PracAAWelding
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Assembly and Rigging',
        active: true,
        questions: PracAAAssemblyandRigging
    },
    {
        type: 'testItem',
        icon: 'ClipboardIcon',
        title: 'Airframe Inspection',
        active: true,
        questions: PracAAAirframeInspection
    },
    {
        type: 'testItem',
        icon: 'TruckIcon',
        title: 'Aircraft Landing Gear Systems',
        active: true,
        questions: PracAAAircraftLandingGearSystems
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Hydraulic and Pneumatic Power Systems',
        active: true,
        questions: PracAAHydraulicandPneumaticPowerSystems
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Cabin Atmosphere Control Systems',
        active: true,
        questions: PracAACabinAtmosphereControlSystems
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Aircraft Instrument Systems',
        active: true,
        questions: PracAAAircraftInstrumentSystems
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Communication and Navigation Systems',
        active: true,
        questions: PracAACommunicationandNavigationSystems
    },
    {
        type: 'testItem',
        icon: 'GasPumpIcon',
        title: 'Aircraft Fuel Systems',
        active: true,
        questions: PracAAAircraftFuelSystems
    },
    {
        type: 'testItem',
        icon: 'NutIcon',
        title: 'Aircraft Electrical Systems',
        active: true,
        questions: PracAAAircraftElectricalSystems
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Position and Warning Systems',
        active: true,
        questions: PracAAPositionandWarningSystems
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Ice and Rain Control Systems',
        active: true,
        questions: PracAAIceandRainControlSystems
    },
    {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Fire Protection Systems',
        active: true,
        questions: PracAAFireProtectionSystems
    },
    {
        type: 'header',
        text: 'Powerplant',
    },
    {
        type: 'testItem',
        icon: 'EngineIcon',
        title: 'Recip Engine',
        active: true,
        questions: [{"Practice Test 1": Question.PracRecip1}, {"Practice Test 2": Question.PracRecip2}, {"Practice Test 3": Question.PracRecip3}, {"Practice Test 4": Question.PracRecip4}, {"Practice RecipCombined": Question.PracRecipCombined}]
    },
    {
        type: 'testItem',
        icon: 'EngineIcon',
        title: 'Turbine Engine',
        active: true,
        questions: [{"Practice Test 1": Question.PracTurbine1}, {"Practice Test 2": Question.PracTurbine2}, {"Practice Test 3": Question.PracTurbine3}, {"Practice Test 4": Question.PracTurbine4}, {"Practice TurbineCombined": Question.PracTurbineCombined}]
    },
    {
        type: 'testItem',
        icon: 'CylinderIcon',
        title: 'Air Systems',
        active: true,
        questions: PracAirsystemPracticeCombinedDeduplicated
    },
    {
        type: 'testItem',
        icon: 'GearIcon',
        title: 'Lubrication Systems',
        active: true,
        questions: [{"Practice Test 1": Question.PracPowerLub1}, {"Practice Test 2": Question.PracPowerLub2}, {"Practice Test 3": Question.PracPowerLub3}, {"Practice Test 4": Question.PracPowerLub4}, {"Practice PowerLubCombined": Question.PracPowerLubCombined}, {"LubRemix": Question.faaPracticeLubRemix}]
    },
    {
        type: 'testItem',
        icon: 'EngineIcon',
        title: 'Induction Systems',
        active: true,
        questions: PracInductionCombinedDeduplicated
    },
     {
        type: 'testItem',
        icon: 'EngineIcon',
        title: 'Instruments',
        active: true,
        questions: PracInstrumentCombinedDeduplicated
    },
    {
        type: 'testItem',
        icon: 'EngineIcon',
        title: 'Exhaust Systems',
        active: true,
        questions: PracExahustPracticeCombinedDeduplicated
    },
    {
        type: 'testItem',
        icon: 'PropellerIcon', // Assuming you still want this to be distinct in data
        title: 'Aircraft Propeller',
        active: true,
        questions: PracPropellerPracticeCombinedDeduplicated
    },
    {
        type: 'testItem',
        icon: 'GasPumpIcon',
        title: 'Aircraft Fuel Systems',
        active: true,
        questions: [{"Practical Questions": Question.PracFuelMeterCombined }, { "Practical Experimental": Question.PracFuelMeterExperimental}]
    },
    {
        type: 'testItem',
        icon: 'NutIcon',
        title: 'Powerplant Electrical Systems',
        active: true,
        questions: PracPowerplantElectricalPracticeCombinedDeduplicated
    },
    {
        type: 'testItem',
        icon: 'MagnetIcon',
        title: 'Starter/Ignition Systems',
        active: true,
        questions: PracStarterSystemsPracticeCombinedDeduplicated
    },
     {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'Fire Protection Systems',
        active: true,
        questions: FireProtectionCombinedDeduplicated
    },
     {
        type: 'testItem',
        icon: 'AirplaneIcon',
        title: 'PowerPlant Inspections',
        active: true,
        questions: PracPowerPlantInspectionsCombinedDeduplicated
    },
    {
        type: 'header',
        text: 'Practice Tests',
    },
    {
        type: 'testItem',
        icon: 'ClipboardIcon',
        title: 'General',
        active: true,
        questions: [{"Practice Test 1": Question.PracTest1}, {"Practice Test 2": Question.PracTest2}, {"Practice Test 3": Question.PracTest3}, {"Practice Test 4": Question.PracTest4}, {"Practice Test 5": Question.PracTest5}, {"Practice Test Malcolm": Question.PracTestMalc} ]
    },
    {
        type: 'testItem',
        icon: 'ClipboardIcon',
        title: 'PowerPlant',
        active: true,
        questions: (sliceNumber?: number) => {
            //this looks like a fisher yates shuffle
            const shuffleArray = (array: any[]) => {
                for (let i = array.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [array[i], array[j]] = [array[j], array[i]];
                }
                return array;
            };

            const allQuestions = [
                ...Question.PracRecipCombined[1],
                ...Question.PracTurbineCombined[1],
                ...PracAirsystemPracticeCombinedDeduplicated[1],
                ...Question.PracPowerLubCombined[1],
                ...PracInductionCombinedDeduplicated[1],
                ...PracInstrumentCombinedDeduplicated[1],
                ...PracExahustPracticeCombinedDeduplicated[1],
                ...PracPropellerPracticeCombinedDeduplicated[1],
                ...Question.PracFuelMeterCombined[1],
                ...PracPowerplantElectricalPracticeCombinedDeduplicated[1],
                ...PracStarterSystemsPracticeCombinedDeduplicated[1],
            ];
            const shuffled = allQuestions.sort(() => 0.5 - Math.random());
            const questionsToReturn = sliceNumber ? shuffled.slice(0, sliceNumber) : shuffled;

            return ['Your Results Will Appear Here', questionsToReturn];
        },
    },
    {
        type: 'header',
        text: 'Favorites',
    },
    {
        type: 'testItem',
        icon: 'UserIcon',
        title: 'Favorite Questions',
        active: true,
        questions: 'FAVORITES'
    },
];

// For the bottom navigation, assuming it's static for now but could also be dynamic
export const navItemsData = [
    {
        label: 'Home',
        icon: 'HomeIcon',
        href: '#',
        active: false,
    },
    {
        label: 'Simulations',
        icon: 'ListBulletsIcon',
        href: '#',
        active: true, // This item is currently active
    },
    {
        label: 'O&P Practice (Beta)',
        icon: 'UserIcon',
        href: '#',
        active: false,
    },
    {
        label: 'Settings',
        icon: 'SettingsIcon',
        href: '#',
        active: false,
    },
];