import * as fs from 'fs';
import * as path from 'path';

// --- CONFIGURATION ---
const fileIdentifier = 'PowerPlantInspectionsCombined'; // Change this to the part of the filename you want to process
const importedObjectName = `Prac${fileIdentifier}`;
// --- END CONFIGURATION ---

type Question = {
  question: string;
  options: string[];
  correct_answer: string;
  fig?: string | string[];
};

/**
 * Deduplicates questions from the PracGenPhysics1Combined object
 * and writes the result to a new file.
 */
async function deduplicateQuestions() {
    const inputFilePath = `./Question.${fileIdentifier}`;
    const outputFileName = `Question.${fileIdentifier}.deduplicated.ts`;
    const outputFilePath = path.join(__dirname, outputFileName);
    const outputObjectName = `${importedObjectName}Deduplicated`;

    try {
        const module = await import(inputFilePath);
        const importedData = module[importedObjectName];

        if (!importedData) {
            console.error(`Error: Could not find export '${importedObjectName}' in '${inputFilePath}'`);
            return;
        }

        // The questions are in the second element of the array
        const questions: Question[] = importedData[1] as Question[];

        const seenQuestions = new Set<string>();
        const uniqueQuestions = questions.filter(q => {
            const questionText = q.question.trim();
            if (seenQuestions.has(questionText)) {
                console.log(`Found duplicate, removing: "${questionText}"`);
                return false;
            } else {
                seenQuestions.add(questionText);
                return true;
            }
        });

        const deduplicatedObject = [
            importedData[0],
            uniqueQuestions
        ];

        const outputContent = `export const ${outputObjectName} = ${JSON.stringify(deduplicatedObject, null, 2)};\n`;

        fs.writeFileSync(outputFilePath, outputContent, 'utf8');

        console.log(`\nDeduplication complete.`);
        console.log(`Original question count: ${questions.length}`);
        console.log(`Deduplicated question count: ${uniqueQuestions.length}`);
        console.log(`Deduplicated questions written to: ${outputFilePath}`);
    } catch (error) {
        console.error(`Failed to process file: ${inputFilePath}`, error);
    }
}

// Run the function
deduplicateQuestions();
