'use client'
import type { mechanicTestsData as mechanicTestsDataType, navItemsData as navItemsDataType } from '@/app/components/AMTQuestionStream/sectionData';

import React, { useState } from 'react';

import Link from "next/link";
import Interactive<PERSON>Chat from './InteractiveOPChat';
import DeleteAccountComponent from './DeleteAccountComponent';
import {
    ArrowLeftIcon,
    NutIcon,
    RulerIcon,
    ScalesIcon,
    CylinderIcon,
    BriefcaseMetalIcon,
    TruckIcon,
    BroomIcon,
    MagnifyingGlassIcon,
    CalculatorIcon,
    ClipboardIcon,
    MagnetIcon,
    AirplaneIcon,
    GearIcon,
    ArrowURightUpIcon,
    EngineIcon,
    PropellerIcon,
    GasPumpIcon,
    HomeIcon,
    ListBulletsIcon,
    ChartLineIcon,
    UserIcon,
    SettingsIcon,
} from '@/app/components/ui/svgIcons';

// Create a mapping from string names to actual React components
const IconComponents = {
    ArrowLeftIcon,
    NutIcon,
    RulerIcon,
    ScalesIcon,
    CylinderIcon,
    BriefcaseMetalIcon,
    TruckIcon,
    BroomIcon,
    MagnifyingGlassIcon,
    CalculatorIcon,
    ClipboardIcon,
    MagnetIcon,
    AirplaneIcon,
    GearIcon,
    ArrowURightUpIcon,
    EngineIcon,
    PropellerIcon,
    GasPumpIcon,
    HomeIcon,
    ListBulletsIcon,
    ChartLineIcon,
    UserIcon,
    SettingsIcon,
};

interface MechanicProps {
    mechanicTestsData: typeof mechanicTestsDataType;
    navItemsData: typeof navItemsDataType;
    onClick: (item: typeof mechanicTestsDataType[0]) => void
    backButtonHandler: () => void
    onHomeClick: () => void,
    defaultLocation?: string
    questionFontSize: number
    setQuestionFontSize: (size: number) => void
}

export default function MechanicTests({ mechanicTestsData, navItemsData, onClick, backButtonHandler, onHomeClick, defaultLocation = 'Tests', questionFontSize, setQuestionFontSize }: MechanicProps) {

    console.log(defaultLocation)

    const [activeNavButton, setActiveNavButton] = useState(defaultLocation); // Default to Tests as active
    const [isSliderVisible, setIsSliderVisible] = useState(false);
    // Remove: const [itemTitleSize, setItemTitleSize] = useState(18); // Default size in pixels (18px is text-lg)

    console.log(activeNavButton)

    const handleDeleteSuccess = () => {
        // Handle post-deletion cleanup
        console.log('Account deleted successfully');
        // You might want to redirect or reset state here
    };

    // Group test items by their headers
    const groupedTests = mechanicTestsData.reduce((acc: { header: string | null | undefined; items: typeof mechanicTestsData[1][] }[], item) => {
        if (item.type === 'header') {
            acc.push({ header: item.text, items: [] });
        } else if (item.type === 'testItem' && acc.length > 0) {
            acc[acc.length - 1].items.push(item);
        }
        return acc;
    }, []);

    return (
        <>
            <div
                className="relative flex size-full min-h-screen flex-col bg-slate-50 justify-between group/design-root overflow-x-hidden"
                style={{ fontFamily: '"Spline Sans", "Noto Sans", sans-serif' }}
            >
                {activeNavButton === 'Tests' && (
                    <div>
                        <div className="flex items-center bg-slate-50 p-4 pb-2 justify-between">
                            <div className="text-[#0d151c] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
                                <button onClick={backButtonHandler} aria-label="Go back">
                                    <ArrowLeftIcon />
                                </button>
                            </div>
                            <h2 className="text-[#0d151c] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Aircraft Maintenance Subjects</h2>
                            <div className="text-[#0d151c] flex size-12 shrink-0 items-center justify-end" data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
                                <button 
                                    onClick={() => setIsSliderVisible(!isSliderVisible)} 
                                    aria-label="Toggle font size slider"
                                    className="flex items-center gap-1 px-3 py-2 rounded-lg bg-white border border-slate-200 hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 shadow-sm"
                                >
                                    <MagnifyingGlassIcon className="w-4 h-4" />
                                    <span className="text-sm font-medium hidden sm:inline">Zoom</span>
                                </button>
                            </div>
                        </div>
                        <p className="text-center text-gray-500 italic px-4 py-2">
                            We are constantly working on adding more content, stay tuned!
                        </p>
                        {isSliderVisible && (
                            <div className="px-4 py-2 bg-slate-100 border-y border-slate-200">
                                <label htmlFor="font-size-slider" className="block text-sm font-medium text-slate-700 mb-1">Item Title Size: {questionFontSize}px</label>
                                <input
                                    id="font-size-slider"
                                    type="range"
                                    min="12"
                                    max="24"
                                    value={questionFontSize}
                                    onChange={(e) => setQuestionFontSize(Number(e.target.value))}
                                    className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"
                                />
                            </div>
                        )}

                        {groupedTests.map((group, groupIndex) => (
                            <React.Fragment key={groupIndex}>
                                <h2 className="text-[#0d151c] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
                                    {group.header}
                                </h2>
                                <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
                                    {group.items.map((item, itemIndex) => {
                                        const IconComponent = IconComponents[item.icon]; // Get the actual component
                                        if (!IconComponent) {
                                            console.warn(`Icon component for "${item.icon}" not found.`);
                                            return null; // Or render a placeholder
                                        }

                                        // Determine active/inactive styles for the test items
                                        const itemClasses = item.active
                                            ? "flex flex-1 gap-3 rounded-lg border border-[#cedce8] bg-slate-50 p-4 items-center cursor-pointer" // Active styles
                                            : "flex flex-1 gap-3 rounded-lg border border-[#e0e0e0] bg-gray-100 p-4 items-center opacity-60 cursor-not-allowed"; // Inactive styles

                                        const iconColorClass = item.active ? "text-[#0d151c]" : "text-[#a0a0a0]"; // Darker for active, lighter for inactive
                                        const titleColorClass = item.active ? "text-[#0d151c]" : "text-[#a0a0a0]"; // Darker for active, lighter for inactive

                                        return (
                                            <div key={itemIndex} className={itemClasses} onClick={() => item.active && onClick(item)}>
                                                <div className={iconColorClass} data-icon={item.icon.replace('Icon', '')} data-size="24px" data-weight="regular">
                                                    <IconComponent /> {/* Render the dynamic icon */}
                                                </div>
                                                <h2 className={titleColorClass + " font-bold leading-tight"} style={{ fontSize: `${questionFontSize}px` }}>{item.title}</h2>
                                            </div>
                                        );
                                    })}
                                </div>
                            </React.Fragment>
                        ))}
                    </div>
                )}

                {activeNavButton === 'O&P Practice (Beta)' && (
                    <div className="flex-1 flex flex-col p-4">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4 text-center">Oral & Practical Practice</h2>
                        <InteractiveOPChat />
                    </div>
                )}

                {/* Settings Panel */}
                {(activeNavButton === 'Settings' || activeNavButton === 'delete-account') && (
                    <div className="flex-1 flex flex-col">
                        <div className="flex items-center bg-slate-50 p-4 pb-2 justify-center">
                            <h2 className="text-[#0d151c] text-lg font-bold leading-tight tracking-[-0.015em]">Settings</h2>
                        </div>
                        <div className="flex-1 flex items-center justify-center">
                            <div className="w-full max-w-md flex flex-col items-center space-y-4 p-4">
                                <button
                                    className="w-full px-6 py-3 bg-gray-700 hover:bg-gray-800 text-white font-medium rounded-lg transition-colors shadow-lg"
                                    onClick={() => {
                                        // Add your sign out logic here
                                        console.log('Sign out clicked');
                                    }}
                                >
                                    Sign Out
                                </button>

                                <Link href="https://malcmind.com/privacy-pages/faa-app" className ="w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors shadow-lg text-center">
                                    <button
                                    >
                                        Privacy Policy
                                    </button>
                                </Link>

                                <DeleteAccountComponent 
                                    onDeleteSuccess={handleDeleteSuccess}
                                    userId="your-user-id" // Pass actual user ID
                                />
                            </div>
                        </div>
                    </div>
                )}

                <div>
                    <div className="flex gap-2 border-t border-[#e7edf4] bg-slate-50 px-4 pb-3 pt-2">
                        {navItemsData.map((navItem, index) => {
                            const NavIconComponent = IconComponents[navItem.icon];
                            if (!NavIconComponent) {
                                console.warn(`Navigation icon component for "${navItem.icon}" not found.`);
                                return null;
                            }

                            // Conditional classes for navigation items
                            const linkBaseClasses = "flex flex-1 flex-col items-center justify-end gap-1 rounded-full";
                            const linkActiveClasses = "text-[#0d151c]";
                            const linkInactiveClasses = "text-[#49749c]";

                            const iconDivBaseClasses = "flex h-8 items-center justify-center";
                            const iconDivActiveClasses = "text-[#0d151c]";
                            const iconDivInactiveClasses = "text-[#49749c]";

                            const pBaseClasses = "text-xs font-medium leading-normal tracking-[0.015em]";
                            const pActiveClasses = "text-[#0d151c]";
                            const pInactiveClasses = "text-[#49749c]";

                            const linkClass = navItem.active ? `${linkBaseClasses} ${linkActiveClasses}` : `${linkBaseClasses} ${linkInactiveClasses}`;
                            const iconDivClass = navItem.active ? `${iconDivBaseClasses} ${iconDivActiveClasses}` : `${iconDivBaseClasses} ${iconDivInactiveClasses}`;
                            const pClass = navItem.active ? `${pBaseClasses} ${pActiveClasses}` : `${pBaseClasses} ${pInactiveClasses}`;


                            return (
                                <a key={index} className={linkClass} href={navItem.href}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        setActiveNavButton(navItem.label);
                                        if (navItem.label === 'Home') {
                                            onHomeClick();
                                        }
                                    }}>
                                    <div className={navItem.label === activeNavButton ? `${iconDivBaseClasses} ${iconDivActiveClasses}` : `${iconDivBaseClasses} ${iconDivInactiveClasses}`} data-icon={navItem.icon.replace('Icon', '')} data-size="24px" data-weight={navItem.label === activeNavButton ? "fill" : "regular"}>
                                        <NavIconComponent />
                                    </div>
                                    <p className={navItem.label === activeNavButton ? `${pBaseClasses} ${pActiveClasses}` : `${pBaseClasses} ${pInactiveClasses}`}>{navItem.label}</p>
                                </a>
                            );
                        })}
                    </div>
                    <div className="h-5 bg-slate-50"></div>
                </div>
            </div>
        </>
    );
}
